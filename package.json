{"name": "all-in-one-illustrator-extension", "version": "0.0.1", "description": "إضافة الكل في واحد لبرنامج Adobe Illustrator - مجموعة شاملة من الأدوات لتسهيل عمل المصممين", "main": "index.html", "scripts": {"build": "echo 'Building extension...'", "package": "echo 'Packaging extension...'", "install-dev": "echo 'Installing in development mode...'", "test": "echo 'Running tests...'"}, "keywords": ["adobe", "illustrator", "extension", "cep", "design", "colors", "arabic", "الكل في واحد"], "author": {"name": "صلاح الدين الدروبي", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/salahadoroobi/all-in-one-illustrator"}, "bugs": {"url": "https://github.com/salahadoroobi/all-in-one-illustrator/issues"}, "homepage": "https://github.com/salahadoroobi/all-in-one-illustrator#readme", "engines": {"node": ">=12.0.0"}, "devDependencies": {"cep-bundler": "^1.0.0", "zxp-provider": "^1.0.0"}, "cep": {"name": "الكل في واحد - All in One", "id": "com.salahadoroobi.allinone", "version": "0.0.1", "hosts": [{"name": "ILST", "version": "[17.0,99.9]"}], "type": "Panel", "width": 300, "height": 400, "minWidth": 250, "minHeight": 200, "maxWidth": 600, "maxHeight": 800}, "directories": {"lib": "./js", "doc": "./docs"}, "files": ["index.html", "manifest.xml", "css/", "js/", "jsx/", "icons/", "README.md"]}