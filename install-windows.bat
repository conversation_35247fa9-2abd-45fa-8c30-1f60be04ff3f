@echo off
echo ========================================
echo All in One Extension Installer
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator...
) else (
    echo This script needs to be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Step 1: Enabling Developer Mode...
REM Enable developer mode in registry
reg add "HKEY_CURRENT_USER\Software\Adobe\CSXS.9" /v PlayerDebugMode /t REG_SZ /d 1 /f
if %errorLevel% == 0 (
    echo Developer mode enabled successfully
) else (
    echo Failed to enable developer mode
    pause
    exit /b 1
)

echo.
echo Step 2: Creating extension directory...
set "EXTENSION_DIR=%APPDATA%\Adobe\CEP\extensions\All in One"
if not exist "%APPDATA%\Adobe\CEP\extensions" (
    mkdir "%APPDATA%\Adobe\CEP\extensions"
)

if exist "%EXTENSION_DIR%" (
    echo Removing existing extension...
    rmdir /s /q "%EXTENSION_DIR%"
)

mkdir "%EXTENSION_DIR%"

echo.
echo Step 3: Copying extension files...
xcopy /E /I /Y "%~dp0*" "%EXTENSION_DIR%"
if %errorLevel% == 0 (
    echo Files copied successfully
) else (
    echo Failed to copy files
    pause
    exit /b 1
)

echo.
echo Step 4: Cleaning up installer files...
del "%EXTENSION_DIR%\install-windows.bat" 2>nul
del "%EXTENSION_DIR%\install-mac.sh" 2>nul

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Close Adobe Illustrator completely
echo 2. Restart Adobe Illustrator
echo 3. Go to Window ^> Extensions ^> All in One
echo.
echo Extension installed to: %EXTENSION_DIR%
echo.
pause
