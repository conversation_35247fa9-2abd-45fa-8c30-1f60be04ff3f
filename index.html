<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الكل في واحد - All in One</title>
    <link rel="stylesheet" href="css/styles.css">
    <script src="js/CSInterface.js"></script>
    <script src="js/themeManager.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>الكل في واحد</h1>
            <p class="version">الإصدار V.0.0.01</p>
            <p class="developer">المطور: صلاح الدين الدروبي</p>
        </header>

        <div class="tabs">
            <button class="tab-button active" data-tab="color-extractor">استخراج الألوان</button>
            <button class="tab-button" data-tab="tools">أدوات أخرى</button>
        </div>

        <div class="tab-content">
            <!-- Color Extractor Tab -->
            <div id="color-extractor" class="tab-panel active">
                <div class="section">
                    <h3>استخراج الألوان</h3>
                    <p class="description">حدد أي عنصر في Illustrator لاستخراج ألوانه</p>
                    
                    <div class="action-buttons">
                        <button id="extract-colors" class="primary-btn">استخراج الألوان من التحديد</button>
                        <button id="get-selection-info" class="secondary-btn">معلومات التحديد</button>
                    </div>

                    <div class="drop-zone" id="drop-zone">
                        <div class="drop-content">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7,10 12,15 17,10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            <p>اسحب وأفلت العناصر هنا</p>
                            <p class="small">أو استخدم الأزرار أعلاه</p>
                        </div>
                    </div>

                    <div class="results" id="color-results">
                        <h4>النتائج:</h4>
                        <div id="color-list" class="color-list">
                            <!-- Colors will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tools Tab -->
            <div id="tools" class="tab-panel">
                <div class="section">
                    <h3>أدوات إضافية</h3>
                    <p class="description">المزيد من الأدوات قريباً...</p>
                    
                    <div class="tool-grid">
                        <div class="tool-card disabled">
                            <h4>أداة القياس</h4>
                            <p>قياس المسافات والأبعاد</p>
                            <span class="coming-soon">قريباً</span>
                        </div>
                        
                        <div class="tool-card disabled">
                            <h4>مولد الألوان</h4>
                            <p>إنشاء لوحات ألوان متناسقة</p>
                            <span class="coming-soon">قريباً</span>
                        </div>
                        
                        <div class="tool-card disabled">
                            <h4>تصدير سريع</h4>
                            <p>تصدير العناصر بصيغ متعددة</p>
                            <span class="coming-soon">قريباً</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="footer">
            <p>&copy; 2024 صلاح الدين الدروبي</p>
        </footer>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
