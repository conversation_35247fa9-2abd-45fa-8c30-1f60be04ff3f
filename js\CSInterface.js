/**
 * CSInterface - v9.0.0
 * Adobe CEP (Common Extensibility Platform) Interface
 */

function CSInterface() {
    "use strict";

    /**
     * User can add this event listener to handle native application theme color changes.
     * Callback function gives extensions ability to fine-tune their theme color after the
     * global theme color has been changed.
     * The callback function should be like below:
     *
     * @example
     * // event registered by extension developer
     * csInterface.addEventListener(CSInterface.THEME_COLOR_CHANGED_EVENT, onAppThemeColorChanged);
     *
     * // callback function written by extension developer
     * function onAppThemeColorChanged(event) {
     *    // Should get a latest HostEnvironment object from application.
     *    var skinInfo = JSON.parse(window.__adobe_cep__.getHostEnvironment()).appSkinInfo;
     *    // Gets the style information such as color info from the skinInfo,
     *    // and redraw all UI controls of your extension according to the style info.
     * }
     */
    CSInterface.THEME_COLOR_CHANGED_EVENT = "com.adobe.csxs.events.ThemeColorChanged";

    /**
     * The host environment data object.
     *
     * @typedef HostEnvironment
     * @type {object}
     * @property {string} appName - The application's name.
     * @property {string} appVersion - The application's version.
     * @property {string} appLocale - The application's locale.
     * @property {string} appUILocale - The application's UI locale.
     * @property {string} appId - The application's ID.
     * @property {boolean} isAppOnline - Whether the application is currently online.
     * @property {object} appSkinInfo - The application's skin information.
     * @property {number} appSkinInfo.panelBackgroundColor.color.red - The application panel's background color (red).
     * @property {number} appSkinInfo.panelBackgroundColor.color.green - The application panel's background color (green).
     * @property {number} appSkinInfo.panelBackgroundColor.color.blue - The application panel's background color (blue).
     * @property {number} appSkinInfo.panelBackgroundColor.color.alpha - The application panel's background color (alpha).
     * @property {number} appSkinInfo.baseFontSize - The base font size of the application.
     * @property {string} appSkinInfo.baseFontFamily - The base font family of the application.
     */

    /**
     * Retrieves information about the host environment in which the extension is currently running.
     *
     * @return {HostEnvironment} A HostEnvironment object.
     */
    CSInterface.prototype.getHostEnvironment = function () {
        var obj = JSON.parse(window.__adobe_cep__.getHostEnvironment());
        return obj;
    };

    /**
     * Closes this extension.
     */
    CSInterface.prototype.closeExtension = function () {
        window.__adobe_cep__.closeExtension();
    };

    /**
     * Retrieves a path for which a constant is defined in the system.
     *
     * @param {string} pathType The path-type constant defined in SystemPath ,
     *
     * @return {string} The platform-specific system path string.
     */
    CSInterface.prototype.getSystemPath = function (pathType) {
        var path = decodeURI(window.__adobe_cep__.getSystemPath(pathType));
        var OSVersion = this.getOSInformation();
        if (OSVersion.indexOf("Windows") >= 0) {
            path = path.replace("file:///", "");
        } else if (OSVersion.indexOf("Mac") >= 0) {
            path = path.replace("file://", "");
        }
        return path;
    };

    /**
     * Evaluates a JavaScript script, which can use the JavaScript DOM
     * of the host application.
     *
     * @param {string} script The JavaScript script.
     * @param {function} callback Optional. A callback function that receives the result of evaluation.
     */
    CSInterface.prototype.evalScript = function (script, callback) {
        if (callback === null || callback === undefined) {
            callback = function (result) {};
        }
        window.__adobe_cep__.evalScript(script, callback);
    };

    /**
     * Retrieves the unique identifier of the application.
     * in which the extension is currently running.
     *
     * @return {string} The unique ID string.
     */
    CSInterface.prototype.getApplicationID = function () {
        var appId = this.getHostEnvironment().appId;
        return appId;
    };

    /**
     * Retrieves host capability information for the application
     * in which the extension is currently running.
     *
     * @return {string} A string containing the host capability information.
     */
    CSInterface.prototype.getHostCapabilities = function () {
        var hostCapabilities = JSON.parse(window.__adobe_cep__.getHostCapabilities());
        return hostCapabilities;
    };

    /**
     * Triggers a CEP event programmatically. Yoy can use it to dispatch
     * an event of a predefined type, or of a type you have defined.
     *
     * @param {CSEvent} event A CSEvent object.
     */
    CSInterface.prototype.dispatchEvent = function (event) {
        if (typeof event.data == "object") {
            event.data = JSON.stringify(event.data);
        }

        window.__adobe_cep__.dispatchEvent(event);
    };

    /**
     * Registers an interest in a CEP event of a particular type, and
     * assigns an event handler.
     * The event infrastructure notifies your extension when events of this type occur,
     * passing the event object to the registered handler function.
     *
     * @param {string} type The name of the event type of interest.
     * @param {function} listener The JavaScript handler function or method.
     * @param {object} obj Optional, the object containing the handler method, if any.
     *        Default is null.
     */
    CSInterface.prototype.addEventListener = function (type, listener, obj) {
        window.__adobe_cep__.addEventListener(type, listener, obj);
    };

    /**
     * Removes a registered event listener.
     *
     * @param {string} type The name of the event type of interest.
     * @param {function} listener The JavaScript handler function or method that was registered.
     * @param {object} obj Optional, the object containing the handler method, if any.
     *        Default is null.
     */
    CSInterface.prototype.removeEventListener = function (type, listener, obj) {
        window.__adobe_cep__.removeEventListener(type, listener, obj);
    };

    /**
     * Loads and launches another extension, or activates the extension if it is already loaded.
     *
     * @param {string} extensionId The extension's unique identifier.
     * @param {string} startupParams Not currently used, pass "".
     *
     * @example
     * To launch the extension "help" with ID "HLP001":
     *   requestOpenExtension("HLP001", "");
     */
    CSInterface.prototype.requestOpenExtension = function (extensionId, params) {
        window.__adobe_cep__.requestOpenExtension(extensionId, params);
    };

    /**
     * Retrieves the list of extensions currently loaded in the current host application.
     * The extension list is initialized once, and remains the same during the lifetime
     * of the CEP session.
     *
     * @param {string} extensionIds Optional, an array of unique identifiers for extensions of interest.
     *        If omitted, retrieves data for all extensions.
     *
     * @return {string} A string containing the extension data in JSON format.
     */
    CSInterface.prototype.getExtensions = function (extensionIds) {
        var extensionIdsStr = JSON.stringify(extensionIds);
        var extensionsStr = window.__adobe_cep__.getExtensions(extensionIdsStr);

        var extensions = JSON.parse(extensionsStr);
        return extensions;
    };

    /**
     * Retrieves network-related preferences.
     *
     * @return {string} A string containing network preferences in JSON format.
     */
    CSInterface.prototype.getNetworkPreferences = function () {
        var result = window.__adobe_cep__.getNetworkPreferences();
        var networkPre = JSON.parse(result);

        return networkPre;
    };

    /**
     * Initializes the resource bundle for this extension with property values
     * for the current application and locale.
     * To support multiple locales, you must define a property file for each locale,
     * containing keyed display-string values for that locale.
     * See localization documentation for Extension Builder and related products.
     *
     * Keys can be in the
     * form <key.name>, and must be UTF-8 encoded.
     *
     * @param {string} resourceBundle The path string to the resource bundle folder.
     */
    CSInterface.prototype.initResourceBundle = function (resourceBundle) {
        var resourceBundleStr = JSON.stringify(resourceBundle);
        window.__adobe_cep__.initResourceBundle(resourceBundleStr);
    };

    /**
     * Writes installation information to a file.
     *
     * @return {string} The file path.
     */
    CSInterface.prototype.dumpInstallationInfo = function () {
        return window.__adobe_cep__.dumpInstallationInfo();
    };

    /**
     * Retrieves version information for the current Operating System,
     * See http://www.useragentstring.com/pages/Chrome/ for Chrome \`navigator.userAgent\` values.
     *
     * @return {string} A string containing the OS version information.
     */
    CSInterface.prototype.getOSInformation = function () {
        var userAgent = navigator.userAgent;

        if (navigator.platform == "Win32" || navigator.platform == "Windows") {
            var winVersion = "Windows";
            var winBit = "";
            if (userAgent.indexOf("Windows NT 5.0") > -1) {
                winVersion = "Windows 2000";
            } else if (userAgent.indexOf("Windows NT 5.1") > -1) {
                winVersion = "Windows XP";
            } else if (userAgent.indexOf("Windows NT 5.2") > -1) {
                winVersion = "Windows Server 2003";
            } else if (userAgent.indexOf("Windows NT 6.0") > -1) {
                winVersion = "Windows Vista";
            } else if (userAgent.indexOf("Windows NT 6.1") > -1) {
                winVersion = "Windows 7";
            } else if (userAgent.indexOf("Windows NT 6.2") > -1) {
                winVersion = "Windows 8";
            } else if (userAgent.indexOf("Windows NT 6.3") > -1) {
                winVersion = "Windows 8.1";
            } else if (userAgent.indexOf("Windows NT 10") > -1) {
                winVersion = "Windows 10";
            }

            if (userAgent.indexOf("WOW64") > -1 || userAgent.indexOf("Win64") > -1) {
                winBit = " 64-bit";
            } else {
                winBit = " 32-bit";
            }

            return winVersion + winBit;
        } else if (navigator.platform == "MacIntel" || navigator.platform == "Macintosh") {
            var result = "Mac OS X";

            if (typeof navigator.userAgentData !== "undefined") {
                var brands = navigator.userAgentData.brands;
                if (brands) {
                    for (var i = 0; i < brands.length; i++) {
                        if (brands[i].brand.indexOf("Chrome") > -1) {
                            var version = brands[i].version;
                            result += " Chrome/" + version;
                            break;
                        }
                    }
                }
            } else {
                if (userAgent.indexOf("Chrome") > -1) {
                    var chrome = userAgent.substring(userAgent.indexOf("Chrome"));
                    result += " " + chrome.substring(0, chrome.indexOf(" "));
                }
            }

            return result;
        }

        return "Unknown Operation System";
    };

    /**
     * Opens a page in the default system browser.
     *
     * Since 4.2.0
     *
     * @param {string} url  The URL of the page/file to open, or the email address.
     * Must use HTTP/HTTPS, file, or mailto protocols. For example:
     *   "http://www.adobe.com"
     *   "https://github.com"
     *   "file:///C:/log.txt"
     *   "mailto:<EMAIL>"
     *
     * @return {number} One of these error codes:\n
     *      <ul>\n
     *          <li>NO_ERROR - 0</li>\n
     *          <li>ERR_UNKNOWN - 1</li>\n
     *          <li>ERR_INVALID_PARAMS - 2</li>\n
     *          <li>ERR_INVALID_URL - 201</li>\n
     *      </ul>\n
     */
    CSInterface.prototype.openURLInDefaultBrowser = function (url) {
        return cep.util.openURLInDefaultBrowser(url);
    };

    /**
     * Retrieves extension ID.
     *
     * Since 4.2.0
     *
     * @return {string} extension ID.
     */
    CSInterface.prototype.getExtensionID = function () {
        return window.__adobe_cep__.getExtensionId();
    };

    /**
     * Retrieves the scale factor of screen.
     * On Windows platform, the value of scale factor might be different from operating system's scale factor,
     * since host application may use its self-defined scale factor.
     *
     * Since 4.2.0
     *
     * @return {number} One of the following float number.
     *      <ul>\n
     *          <li> -1.0 when error occurs </li>\n
     *          <li> 1.0 means normal screen </li>\n
     *          <li> >1.0 means HiDPI screen </li>\n
     *      </ul>\n
     */
    CSInterface.prototype.getScaleFactor = function () {
        return window.__adobe_cep__.getScaleFactor();
    };

    /**
     * Set a handler to detect any changes of scale factor. This only works on Mac.
     *
     * Since 4.2.0
     *
     * @param {function} handler   The function to be called when scale factor is changed.
     *
     */
    CSInterface.prototype.setScaleFactorChangedHandler = function (handler) {
        window.__adobe_cep__.setScaleFactorChangedHandler(handler);
    };

    /**
     * Retrieves current API version.
     *
     * Since 4.2.0
     *
     * @return {string} Current API version.
     *
     */
    CSInterface.prototype.getCurrentApiVersion = function () {
        return JSON.parse(window.__adobe_cep__.getCurrentApiVersion());
    };

    /**
     * Set panel flyout menu by an XML.
     *
     * Since 5.2.0
     *
     * Register a callback function for "com.adobe.csxs.events.flyoutMenuClicked" to get notified when a
     * menu item is clicked.
     * The "data" attribute of event is an object which contains "menuId" and "menuLabel" attributes.
     *
     * Register callback:
     * csInterface.addEventListener("com.adobe.csxs.events.flyoutMenuClicked", onFlyoutMenuClicked);
     *
     * @param {string} menu     A XML string which describes menu structure.
     * An example menu XML:
     * <Menu>
     *   <MenuItem Id="menuItemId1" Label="TestExample1" Enabled="true" Checked="false"/>
     *   <MenuItem Label="TestExample2">
     *     <MenuItem Id="menuItemId2" Label="TestExample2-1" Enabled="true" Checked="true"/>
     *     <MenuItem Id="menuItemId3" Label="TestExample2-2" Enabled="false" Checked="false"/>
     *   </MenuItem>
     *   <MenuItem Label="---" />
     *   <MenuItem Id="menuItemId4" Label="TestExample3" Enabled="true" Checked="false"/>
     * </Menu>
     *
     */
    CSInterface.prototype.setPanelFlyoutMenu = function (menu) {
        if ("string" != typeof menu) {
            return;
        }

        window.__adobe_cep__.invokeSync("setPanelFlyoutMenu", menu);
    };

    /**
     * Updates a menu item in the extension window's flyout menu, by setting the enabled
     * and selection status.
     *
     * Since 5.2.0
     *
     * @param {string} menuItemLabel    The menu item label.
     * @param {boolean} enabled         true to enable the item, false to disable it (gray it out).
     * @param {boolean} checked         true to select the item, false to deselect it.
     *
     * @return {boolean} true if the menu item was updated successfully, false if no such menu item.
     */
    CSInterface.prototype.updatePanelMenuItem = function (menuItemLabel, enabled, checked) {
        var ret = false;
        if ("string" != typeof menuItemLabel) {
            return ret;
        }
        if ("boolean" != typeof enabled) {
            return ret;
        }
        if ("boolean" != typeof checked) {
            return ret;
        }

        var itemStatus = new MenuItemStatus(menuItemLabel, enabled, checked);
        ret = window.__adobe_cep__.invokeSync("updatePanelMenuItem", JSON.stringify(itemStatus));
        return ret;
    };

    /**
     * Set context menu by XML string.
     *
     * Since 5.2.0
     *
     * There are a number of conventions used to communicate what type of menu item to create and how it should be handled.
     * - an item without menu ID or menu action, but with sub-menu items, is a disabled sub-menu parent
     * - an item with "-" or "---" as the label is a separator
     * - an item without a sub-menu, but with a menu ID or menu action, is an enabled menu item
     * - an item with a menu action string is handled by the extension
     * - an item with a menu ID is sent back to the host application
     * - a menu item with a menu ID of "ILST_CUT" or "ILST_COPY" or "ILST_PASTE" is a special case
     *   and is handled by the host application
     * - a menu item with a menu ID of "ILST_CLEAR" is a special case and is handled by the host application
     * - a menu item with a menu ID of "ILST_SELECTALL" is a special case and is handled by the host application
     *
     * @param {string} menu     A XML string which describes menu structure.
     * @param {function} callback   The callback function which is called when a menu item is clicked. The only parameter is the returned ID of clicked menu item.
     *
     * @description An example menu XML:
     * <Menu>
     *   <MenuItem Id="menuItemId1" Label="TestExample1" Enabled="true" Checked="false"/>
     *   <MenuItem Label="TestExample2">
     *     <MenuItem Id="menuItemId2" Label="TestExample2-1" Enabled="true" Checked="true"/>
     *     <MenuItem Id="menuItemId3" Label="TestExample2-2" Enabled="false" Checked="false"/>
     *   </MenuItem>
     *   <MenuItem Label="---" />
     *   <MenuItem Id="menuItemId4" Label="TestExample3" Enabled="true" Checked="false"/>
     * </Menu>
     */
    CSInterface.prototype.setContextMenu = function (menu, callback) {
        if ("string" != typeof menu) {
            return;
        }

        window.__adobe_cep__.invokeAsync("setContextMenu", menu, callback);
    };

    /**
     * Set context menu by JSON string.
     *
     * Since 6.0.0
     *
     * @param {string} menu     A JSON string which describes menu structure.
     * @param {function} callback   The callback function which is called when a menu item is clicked. The only parameter is the returned ID of clicked menu item.
     *
     * @description An example menu JSON:
     *
     * {
     *      "menu": [
     *          {
     *              "id": "menuItemId1",
     *              "label": "testExample1",
     *              "enabled": true,
     *              "checkable": true,
     *              "checked": false,
     *              "icon": "./image/small_16X16.png"
     *          },
     *          {
     *              "id": "menuItemId2",
     *              "label": "testExample2",
     *              "menu": [
     *                  {
     *                      "id": "menuItemId2-1",
     *                      "label": "testExample2-1",
     *                      "menu": [
     *                          {
     *                              "id": "menuItemId2-1-1",
     *                              "label": "testExample2-1-1",
     *                              "enabled": false,
     *                              "checkable": true,
     *                              "checked": true
     *                          }
     *                      ]
     *                  },
     *                  {
     *                      "id": "menuItemId2-2",
     *                      "label": "testExample2-2",
     *                      "enabled": true,
     *                      "checkable": true,
     *                      "checked": true
     *                  }
     *              ]
     *          },
     *          {
     *              "label": "---"
     *          },
     *          {
     *              "id": "menuItemId3",
     *              "label": "testExample3",
     *              "enabled": false,
     *              "checkable": true,
     *              "checked": false
     *          }
     *      ]
     *  }
     *
     */
    CSInterface.prototype.setContextMenuByJSON = function (menu, callback) {
        if ("string" != typeof menu) {
            return;
        }

        window.__adobe_cep__.invokeAsync("setContextMenuByJSON", menu, callback);
    };

    /**
     * Updates a context menu item by setting the enabled and selection status.
     *
     * Since 5.2.0
     *
     * @param {string} menuItemID       The menu item ID.
     * @param {boolean} enabled         true to enable the item, false to disable it (gray it out).
     * @param {boolean} checked         true to select the item, false to deselect it.
     */
    CSInterface.prototype.updateContextMenuItem = function (menuItemID, enabled, checked) {
        var itemStatus = new ContextMenuItemStatus(menuItemID, enabled, checked);
        ret = window.__adobe_cep__.invokeSync("updateContextMenuItem", JSON.stringify(itemStatus));
    };

    /**
     * Get the visibility status of an extension window.
     *
     * Since 6.0.0
     *
     * @return {boolean} true if the extension window is visible; false if the extension window is hidden.
     */
    CSInterface.prototype.isWindowVisible = function () {
        return window.__adobe_cep__.invokeSync("isWindowVisible", "");
    };

    /**
     * Resize extension's content to the specified dimensions.
     * 1. Works with modal and modeless extensions in all Adobe products.
     * 2. Extension's manifest min/max size constraints apply and take precedence.
     * 3. For panel extensions
     *    3.1 This works in all Adobe products except:
     *        * Premiere Pro
     *        * Prelude
     *        * After Effects for a panel launched from the menu Window->Extensions
     *
     *    3.2 When the panel is in a docked state, it will be changed to undocked state.
     *    3.3 When the panel is in a floating state, the change is applied immediately.
     *
     * Since 6.0.0
     *
     * @param {number} width    The new width
     * @param {number} height   The new height
     */
    CSInterface.prototype.resizeContent = function (width, height) {
        window.__adobe_cep__.resizeContent(width, height);
    };

    /**
     * Register the invalid certificate callback for an extension.
     * This callback will be triggered when the extension tries to access the web site that contains the invalid certificate on the main frame.
     * But if the extension does not call this function and tries to access the web site containing the invalid certificate, a default error page will be shown.
     *
     * Since 6.1.0
     *
     * @param {function} callback the callback function
     */
    CSInterface.prototype.registerInvalidCertificateCallback = function (callback) {
        return window.__adobe_cep__.registerInvalidCertificateCallback(callback);
    };

    /**
     * Register an interest in some key events to prevent them from being sent to the host application.
     *
     * This function works with modeless extensions and panel extensions.
     * Generally all the key events will be sent to the host application for these two extensions if the current focused element is not text input or dropdown,
     * If you want to intercept some key events and want them to be handled in the extension, please call this function in advance to prevent them being sent to the host application.
     *
     * Since 6.1.0
     *
     * @param {Array} keyEventsInterest      An array of JSON object which contains keyCode and ctrlKey, metaKey, shiftKey and altKey. E.g. [{keyCode: 48, ctrlKey: true}]
     *
     */
    CSInterface.prototype.registerKeyEventsInterest = function (keyEventsInterest) {
        return window.__adobe_cep__.registerKeyEventsInterest(JSON.stringify(keyEventsInterest));
    };

    /**
     * Set the title of the extension window.
     * This function works with modal and modeless extensions in all Adobe products, and panel extensions in Photoshop, InDesign, InCopy, Illustrator, Flash Pro and Dreamweaver.
     *
     * Since 6.1.0
     *
     * @param {string} title The window title.
     */
    CSInterface.prototype.setWindowTitle = function (title) {
        window.__adobe_cep__.invokeSync("setWindowTitle", title);
    };

    /**
     * Get the title of the extension window.
     * This function works with modal and modeless extensions in all Adobe products, and panel extensions in Photoshop, InDesign, InCopy, Illustrator, Flash Pro and Dreamweaver.
     *
     * Since 6.1.0
     *
     * @return {string} The window title.
     */
    CSInterface.prototype.getWindowTitle = function () {
        return window.__adobe_cep__.invokeSync("getWindowTitle", "");
    };
}

/**
 * Class CSEvent.
 * You can use it to dispatch a standard CEP event.
 *
 * @param {string} type        Event type.
 * @param {string} scope       The scope for the event, can be "GLOBAL" or "APPLICATION".
 * @param {string} appId       The unique identifier of the application that generated the event.
 * @param {string} extensionId The unique identifier of the extension that generated the event.
 */
function CSEvent(type, scope, appId, extensionId) {
    this.type = type;
    this.scope = scope;
    this.appId = appId;
    this.extensionId = extensionId;
    this.data = "";
}

/**
 * Class SystemPath
 * Stores operating-system-specific location constants for use in the
 * CSInterface.getSystemPath() method.
 * @return A new SystemPath object.
 */
function SystemPath() {}

/** The path to user data.  */
SystemPath.USER_DATA = "userData";

/** The path to common files for Adobe applications.  */
SystemPath.COMMON_FILES = "commonFiles";

/** The path to the user's default document folder.  */
SystemPath.MY_DOCUMENTS = "myDocuments";

/** @deprecated. Use SystemPath.Extension.  */
SystemPath.APPLICATION = "application";

/** The path to current extension.  */
SystemPath.EXTENSION = "extension";

/** The path to hosting application's executable.  */
SystemPath.HOST_APPLICATION = "hostApplication";

/**
 * Class ColorType
 * Stores color-type constants.
 */
function ColorType() {}

/** RGB color type. */
ColorType.RGB = "rgb";

/** Gradient color type. */
ColorType.GRADIENT = "gradient";

/** None color type. */
ColorType.NONE = "none";

/**
 * Class RGBColor
 * Stores an RGB color with red, green, blue, and alpha values.
 * All values are in the range [0...255], with 0 being the
 * minimum value and 255 being the maximum value.
 *
 * @param {number} red   The red value, in the range [0...255].
 * @param {number} green The green value, in the range [0...255].
 * @param {number} blue  The blue value, in the range [0...255].
 * @param {number} alpha The alpha (transparency) value, in the range [0...255].
 *                       The default, 255, means that the color is fully opaque.
 *
 * @return A new RGBColor object.
 */
function RGBColor(red, green, blue, alpha) {
    this.red = red;
    this.green = green;
    this.blue = blue;
    this.alpha = alpha;
}

/**
 * Class Direction
 * A point value  in which the y component is 0 and the x component
 * is positive or negative for a right or left direction,
 * or the x component is 0 and the y component is positive or negative for
 * an up or down direction.
 *
 * @param {number} x     The horizontal component of the point.
 * @param {number} y     The vertical component of the point.
 *
 * @return A new Direction object.
 */
function Direction(x, y) {
    this.x = x;
    this.y = y;
}

/**
 * Stores flyout menu item status
 *
 * Since 5.2.0
 *
 * @param {string} menuItemLabel    The menu item label.
 * @param {boolean} enabled         true to enable the item, false to disable it (gray it out).
 * @param {boolean} checked         true to select the item, false to deselect it.
 *
 * @return A new MenuItemStatus object.
 */
function MenuItemStatus(menuItemLabel, enabled, checked) {
    this.menuItemLabel = menuItemLabel;
    this.enabled = enabled;
    this.checked = checked;
}

/**
 * Stores the status of the context menu item
 *
 * Since 5.2.0
 *
 * @param {string} menuItemID       The menu item ID.
 * @param {boolean} enabled         true to enable the item, false to disable it (gray it out).
 * @param {boolean} checked         true to select the item, false to deselect it.
 *
 * @return A new ContextMenuItemStatus object.
 */
function ContextMenuItemStatus(menuItemID, enabled, checked) {
    this.menuItemID = menuItemID;
    this.enabled = enabled;
    this.checked = checked;
}

/**
 * Class AppSkinInfo
 * Stores the information related to the application's appearance, such as color info
 *
 * @param {RGBColor} panelBackgroundColor   The application panel's background color.
 * @param {RGBColor} panelForegroundColor   The application panel's foreground color.
 * @param {RGBColor} appBarBackgroundColor  The application bar's background color.
 * @param {RGBColor} appBarForegroundColor  The application bar's foreground color.
 * @param {number}   baseFontSize           The base font size of the application.
 * @param {string}   baseFontFamily         The base font family of the application.
 *
 * @return A new AppSkinInfo object.
 */
function AppSkinInfo(panelBackgroundColor, panelForegroundColor, appBarBackgroundColor, appBarForegroundColor, baseFontSize, baseFontFamily) {
    this.panelBackgroundColor = panelBackgroundColor;
    this.panelForegroundColor = panelForegroundColor;
    this.appBarBackgroundColor = appBarBackgroundColor;
    this.appBarForegroundColor = appBarForegroundColor;
    this.baseFontSize = baseFontSize;
    this.baseFontFamily = baseFontFamily;
}

/**
 * Class HostCapabilities
 * Stores the information related to the host application's capabilities.
 *
 * @return A new HostCapabilities object.
 */
function HostCapabilities() {}

/** Indicates the host application supports the image brush feature. */
HostCapabilities.EXTENDED_PANEL_MENU = "EXTENDED_PANEL_MENU";

/** Indicates the host application supports the extended panel menu feature. */
HostCapabilities.EXTENDED_PANEL_ICONS = "EXTENDED_PANEL_ICONS";

/** Indicates the host application supports the delegate APE feature. */
HostCapabilities.DELEGATE_APE = "DELEGATE_APE";

/** Indicates the host application supports the workspace management feature. */
HostCapabilities.SUPPORT_HTML_EXTENSIONS = "SUPPORT_HTML_EXTENSIONS";

/** Indicates the host application supports the HTML extensions feature. */
HostCapabilities.DISABLE_FLASH_EXTENSIONS = "DISABLE_FLASH_EXTENSIONS";

/**
 * Class ApiVersion
 * Stores current api version.
 *
 * Since 4.2.0
 *
 * @return ApiVersion object.
 */
function ApiVersion(major, minor, micro) {
    this.major = major;
    this.minor = minor;
    this.micro = micro;
}

/**
 *
 * Since 6.1.0
 *
 * Stores operating-system-specific location constants for use in the
 * CSInterface.getSystemPath() method.
 */
function SystemPath() {}

/** The path to user data.  */
SystemPath.USER_DATA = "userData";

/** The path to common files for Adobe applications.  */
SystemPath.COMMON_FILES = "commonFiles";

/** The path to the user's default document folder.  */
SystemPath.MY_DOCUMENTS = "myDocuments";

/** @deprecated. Use SystemPath.Extension.  */
SystemPath.APPLICATION = "application";

/** The path to current extension.  */
SystemPath.EXTENSION = "extension";

/** The path to hosting application's executable.  */
SystemPath.HOST_APPLICATION = "hostApplication";
