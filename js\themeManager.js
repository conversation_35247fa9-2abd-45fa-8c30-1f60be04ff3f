/**
 * Theme Manager for All in One Extension
 * Handles theme changes and color management
 */

(function() {
    'use strict';

    var ThemeManager = {
        
        // Initialize theme management
        init: function() {
            this.updateTheme();
            this.setupThemeListener();
        },

        // Update theme based on host application
        updateTheme: function() {
            try {
                var csInterface = new CSInterface();
                var hostEnvironment = csInterface.getHostEnvironment();
                
                if (hostEnvironment && hostEnvironment.appSkinInfo) {
                    var skinInfo = hostEnvironment.appSkinInfo;
                    this.applyTheme(skinInfo);
                }
            } catch (error) {
                console.warn('Could not get host environment, using default theme');
                this.applyDefaultTheme();
            }
        },

        // Apply theme based on skin info
        applyTheme: function(skinInfo) {
            var panelBg = skinInfo.panelBackgroundColor.color;
            var isLightTheme = (panelBg.red + panelBg.green + panelBg.blue) > 384; // 128 * 3

            var root = document.documentElement;
            
            if (isLightTheme) {
                this.applyLightTheme(root, skinInfo);
            } else {
                this.applyDarkTheme(root, skinInfo);
            }

            // Apply base font settings
            if (skinInfo.baseFontFamily) {
                root.style.setProperty('--base-font-family', skinInfo.baseFontFamily);
            }
            
            if (skinInfo.baseFontSize) {
                root.style.setProperty('--base-font-size', skinInfo.baseFontSize + 'px');
            }
        },

        // Apply light theme
        applyLightTheme: function(root, skinInfo) {
            var panelBg = skinInfo.panelBackgroundColor.color;
            
            root.style.setProperty('--bg-primary', this.rgbToHex(panelBg));
            root.style.setProperty('--bg-secondary', this.lighten(panelBg, 10));
            root.style.setProperty('--bg-tertiary', this.lighten(panelBg, 20));
            
            root.style.setProperty('--text-primary', '#333333');
            root.style.setProperty('--text-secondary', '#666666');
            root.style.setProperty('--text-muted', '#999999');
            
            root.style.setProperty('--border-color', '#cccccc');
            root.style.setProperty('--accent-color', '#0066cc');
            
            document.body.classList.add('light-theme');
            document.body.classList.remove('dark-theme');
        },

        // Apply dark theme
        applyDarkTheme: function(root, skinInfo) {
            var panelBg = skinInfo.panelBackgroundColor.color;
            
            root.style.setProperty('--bg-primary', this.rgbToHex(panelBg));
            root.style.setProperty('--bg-secondary', this.lighten(panelBg, 15));
            root.style.setProperty('--bg-tertiary', this.lighten(panelBg, 25));
            
            root.style.setProperty('--text-primary', '#e0e0e0');
            root.style.setProperty('--text-secondary', '#b0b0b0');
            root.style.setProperty('--text-muted', '#7a7a7a');
            
            root.style.setProperty('--border-color', '#5a5a5a');
            root.style.setProperty('--accent-color', '#4a90e2');
            
            document.body.classList.add('dark-theme');
            document.body.classList.remove('light-theme');
        },

        // Apply default theme when host environment is not available
        applyDefaultTheme: function() {
            var root = document.documentElement;
            
            root.style.setProperty('--bg-primary', '#2c2c2c');
            root.style.setProperty('--bg-secondary', '#3a3a3a');
            root.style.setProperty('--bg-tertiary', '#4a4a4a');
            
            root.style.setProperty('--text-primary', '#e0e0e0');
            root.style.setProperty('--text-secondary', '#b0b0b0');
            root.style.setProperty('--text-muted', '#7a7a7a');
            
            root.style.setProperty('--border-color', '#5a5a5a');
            root.style.setProperty('--accent-color', '#4a90e2');
            
            document.body.classList.add('dark-theme');
            document.body.classList.remove('light-theme');
        },

        // Setup theme change listener
        setupThemeListener: function() {
            var self = this;
            
            try {
                var csInterface = new CSInterface();
                csInterface.addEventListener(CSInterface.THEME_COLOR_CHANGED_EVENT, function() {
                    self.updateTheme();
                });
            } catch (error) {
                console.warn('Could not setup theme listener');
            }
        },

        // Utility: Convert RGB object to hex string
        rgbToHex: function(rgb) {
            var r = Math.round(rgb.red);
            var g = Math.round(rgb.green);
            var b = Math.round(rgb.blue);
            
            return "#" + 
                   ("0" + r.toString(16)).slice(-2) + 
                   ("0" + g.toString(16)).slice(-2) + 
                   ("0" + b.toString(16)).slice(-2);
        },

        // Utility: Lighten a color by a percentage
        lighten: function(rgb, percent) {
            var r = Math.min(255, Math.round(rgb.red + (255 - rgb.red) * percent / 100));
            var g = Math.min(255, Math.round(rgb.green + (255 - rgb.green) * percent / 100));
            var b = Math.min(255, Math.round(rgb.blue + (255 - rgb.blue) * percent / 100));
            
            return this.rgbToHex({red: r, green: g, blue: b});
        },

        // Utility: Darken a color by a percentage
        darken: function(rgb, percent) {
            var r = Math.max(0, Math.round(rgb.red * (100 - percent) / 100));
            var g = Math.max(0, Math.round(rgb.green * (100 - percent) / 100));
            var b = Math.max(0, Math.round(rgb.blue * (100 - percent) / 100));
            
            return this.rgbToHex({red: r, green: g, blue: b});
        }
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            ThemeManager.init();
        });
    } else {
        ThemeManager.init();
    }

    // Make ThemeManager globally available
    window.ThemeManager = ThemeManager;

})();
