# دليل استكشاف الأخطاء - All in One Extension

## المشاكل الشائعة وحلولها

### 1. الإضافة لا تظهر في قائمة Extensions

#### الحل الأول: تفعيل وضع المطور
**Windows:**
```
1. اضغط Win + R
2. اكتب regedit واضغط Enter
3. انتقل إلى: HKEY_CURRENT_USER\Software\Adobe\CSXS.9
4. انقر بالزر الأيمن > New > String Value
5. اكتب PlayerDebugMode
6. انقر مرتين واكتب 1
```

**macOS:**
```bash
defaults write com.adobe.CSXS.9 PlayerDebugMode 1
```

#### الحل الثاني: التحقق من مسار التثبيت
تأكد من أن المجلد موجود في:

**Windows:**
- `C:\Users\<USER>\AppData\Roaming\Adobe\CEP\extensions\All in One`
- أو `C:\Program Files (x86)\Common Files\Adobe\CEP\extensions\All in One`

**macOS:**
- `~/Library/Application Support/Adobe/CEP/extensions/All in One`
- أو `/Library/Application Support/Adobe/CEP/extensions/All in One`

#### الحل الثالث: التحقق من إصدار Illustrator
- الإضافة تتطلب Illustrator 2018 أو أحدث
- تحقق من إصدارك: Help > About Illustrator

### 2. الإضافة تظهر لكن لا تفتح

#### تحقق من الملفات المطلوبة:
```
All in One/
├── manifest.xml
├── index.html
├── css/styles.css
├── js/main.js
├── js/CSInterface.js
├── js/themeManager.js
└── jsx/hostscript.jsx
```

#### تحقق من الأذونات:
**Windows:** انقر بالزر الأيمن على المجلد > Properties > Security
**macOS:** `chmod -R 755 "All in One"`

### 3. رسالة خطأ "Extension not loaded"

#### الحل:
1. أغلق Illustrator تماماً
2. احذف مجلد الإضافة
3. أعد تثبيت الإضافة
4. أعد تشغيل Illustrator

### 4. الإضافة تفتح لكن لا تعمل

#### افتح Developer Tools:
1. انقر بالزر الأيمن داخل الإضافة
2. اختر "Inspect" أو اضغط F12
3. ابحث عن أخطاء في Console

#### الأخطاء الشائعة:
- `CSInterface is not defined`: تأكد من وجود ملف CSInterface.js
- `evalScript failed`: تأكد من وجود ملف hostscript.jsx
- `Permission denied`: تحقق من أذونات الملفات

### 5. الإضافة لا تستخرج الألوان

#### تأكد من:
1. وجود عناصر محددة في Illustrator
2. أن العناصر تحتوي على ألوان (fill أو stroke)
3. أن العناصر ليست مقفلة (locked)

### 6. مشاكل الثيم والألوان

#### إذا كانت الألوان غير صحيحة:
1. أعد تشغيل الإضافة
2. غير ثيم Illustrator وأعده
3. تحقق من إعدادات العرض

### 7. مشاكل الخطوط العربية

#### إذا لم تظهر النصوص العربية بشكل صحيح:
1. تأكد من تثبيت خطوط عربية على النظام
2. أعد تشغيل Illustrator
3. تحقق من إعدادات اللغة في النظام

## أدوات التشخيص

### 1. فحص سجل الأخطاء
**Windows:** Event Viewer > Windows Logs > Application
**macOS:** Console.app > System Reports

### 2. فحص ملفات CEP
تحقق من وجود ملفات CEP في:
**Windows:** `C:\Program Files (x86)\Common Files\Adobe\CEP`
**macOS:** `/Library/Application Support/Adobe/CEP`

### 3. اختبار الإضافة
```javascript
// افتح Developer Tools واكتب:
console.log('Extension loaded');
csInterface.evalScript('alert("Test")');
```

## إعادة التثبيت الكاملة

### 1. إزالة الإضافة
```bash
# Windows
rmdir /s "C:\Users\<USER>\AppData\Roaming\Adobe\CEP\extensions\All in One"

# macOS
rm -rf "~/Library/Application Support/Adobe/CEP/extensions/All in One"
```

### 2. تنظيف Registry (Windows فقط)
```
1. افتح regedit
2. انتقل إلى: HKEY_CURRENT_USER\Software\Adobe\CSXS.9
3. احذف PlayerDebugMode
4. أعد إنشاؤه بقيمة 1
```

### 3. إعادة التثبيت
استخدم ملفات التثبيت التلقائي أو اتبع التعليمات اليدوية.

## طلب المساعدة

إذا لم تحل هذه الخطوات المشكلة:

1. **اجمع المعلومات التالية:**
   - إصدار Illustrator
   - نظام التشغيل
   - رسالة الخطأ الدقيقة
   - خطوات إعادة إنتاج المشكلة

2. **تحقق من:**
   - ملف manifest.xml سليم
   - جميع الملفات موجودة
   - الأذونات صحيحة

3. **اتصل بالدعم:**
   - أرسل تفاصيل المشكلة
   - أرفق لقطات شاشة
   - أرفق ملف log إن وجد

---
© 2024 صلاح الدين الدروبي
