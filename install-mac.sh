#!/bin/bash

echo "========================================"
echo "All in One Extension Installer"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Step 1: Enabling Developer Mode...${NC}"
# Enable developer mode
defaults write com.adobe.CSXS.9 PlayerDebugMode 1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Developer mode enabled successfully${NC}"
else
    echo -e "${RED}Failed to enable developer mode${NC}"
    exit 1
fi

echo
echo -e "${YELLOW}Step 2: Creating extension directory...${NC}"
EXTENSION_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions/All in One"

# Create CEP extensions directory if it doesn't exist
mkdir -p "$HOME/Library/Application Support/Adobe/CEP/extensions"

# Remove existing extension if it exists
if [ -d "$EXTENSION_DIR" ]; then
    echo "Removing existing extension..."
    rm -rf "$EXTENSION_DIR"
fi

# Create extension directory
mkdir -p "$EXTENSION_DIR"

echo
echo -e "${YELLOW}Step 3: Copying extension files...${NC}"
# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Copy all files except installer scripts
rsync -av --exclude='install-*.sh' --exclude='install-*.bat' "$SCRIPT_DIR/" "$EXTENSION_DIR/"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Files copied successfully${NC}"
else
    echo -e "${RED}Failed to copy files${NC}"
    exit 1
fi

echo
echo -e "${YELLOW}Step 4: Setting permissions...${NC}"
# Set proper permissions
chmod -R 755 "$EXTENSION_DIR"

echo
echo "========================================"
echo -e "${GREEN}Installation completed successfully!${NC}"
echo "========================================"
echo
echo "Next steps:"
echo "1. Close Adobe Illustrator completely"
echo "2. Restart Adobe Illustrator"
echo "3. Go to Window > Extensions > All in One"
echo
echo "Extension installed to: $EXTENSION_DIR"
echo

# Ask if user wants to open the extension directory
read -p "Do you want to open the extension directory? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    open "$EXTENSION_DIR"
fi
