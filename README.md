# الكل في واحد - All in One
## إضافة Adobe Illustrator

### معلومات الإضافة
- **الاسم:** الكل في واحد (All in One)
- **المطور:** صلاح الدين الدروبي
- **الإصدار:** V.0.0.01
- **التوافق:** Adobe Illustrator CS6 وما بعده

### الوصف
إضافة شاملة لبرنامج Adobe Illustrator تهدف إلى تسهيل عمل المصممين من خلال مجموعة من الأدوات المفيدة.

### الميزات الحالية

#### 1. استخراج الألوان
- استخراج الألوان من العناصر المحددة في Illustrator
- دعم أنواع مختلفة من الألوان (RGB, CMYK, Grayscale, Spot Colors)
- عرض الألوان بصيغ مختلفة (Hex, RGB)
- إمكانية نسخ قيم الألوان بنقرة واحدة
- واجهة سحب وإفلات سهلة الاستخدام

#### 2. معلومات التحديد
- عرض معلومات مفصلة عن العناصر المحددة
- عدد العناصر ونوعها
- الأبعاد والموقع

### الميزات القادمة
- أداة القياس للمسافات والأبعاد
- مولد الألوان لإنشاء لوحات ألوان متناسقة
- تصدير سريع بصيغ متعددة
- المزيد من الأدوات المفيدة

### متطلبات التثبيت
1. Adobe Illustrator CS6 أو أحدث
2. نظام التشغيل Windows أو macOS
3. تفعيل وضع المطور في Illustrator

### طريقة التثبيت

#### الطريقة الأولى: التثبيت التلقائي (الأسهل)

**على Windows:**
1. انقر بالزر الأيمن على `install-windows.bat`
2. اختر "Run as administrator"
3. اتبع التعليمات على الشاشة

**على macOS:**
1. افتح Terminal
2. انتقل إلى مجلد الإضافة
3. اكتب: `chmod +x install-mac.sh && ./install-mac.sh`
4. اتبع التعليمات على الشاشة

#### الطريقة الثانية: التثبيت اليدوي
1. أغلق Adobe Illustrator تماماً
2. انسخ مجلد الإضافة إلى مجلد الإضافات:
   
   **Windows:**
   ```
   C:\Program Files (x86)\Common Files\Adobe\CEP\extensions\
   ```
   أو
   ```
   C:\Users\<USER>\AppData\Roaming\Adobe\CEP\extensions\
   ```
   
   **macOS:**
   ```
   /Library/Application Support/Adobe/CEP/extensions/
   ```
   أو
   ```
   ~/Library/Application Support/Adobe/CEP/extensions/
   ```

3. تفعيل وضع المطور:
   
   **Windows:**
   - افتح Registry Editor
   - انتقل إلى: `HKEY_CURRENT_USER\Software\Adobe\CSXS.9`
   - أنشئ مفتاح جديد اسمه `PlayerDebugMode` من نوع `String`
   - اضبط القيمة على `1`
   
   **macOS:**
   - افتح Terminal
   - اكتب: `defaults write com.adobe.CSXS.9 PlayerDebugMode 1`

4. أعد تشغيل Illustrator
5. اذهب إلى `Window > Extensions > All in One`

#### الطريقة الثانية: استخدام ZXPInstaller
1. حمل ZXPInstaller من الموقع الرسمي
2. اسحب ملف .zxp إلى ZXPInstaller
3. اتبع التعليمات

### طريقة الاستخدام

#### استخراج الألوان:
1. حدد العناصر التي تريد استخراج ألوانها في Illustrator
2. افتح لوحة "الكل في واحد"
3. انقر على "استخراج الألوان من التحديد"
4. ستظهر الألوان في القائمة أدناه
5. انقر على أي لون لنسخ قيمته

#### معلومات التحديد:
1. حدد العناصر في Illustrator
2. انقر على "معلومات التحديد"
3. ستظهر معلومات مفصلة عن التحديد

### استكشاف الأخطاء

#### الإضافة لا تظهر في القائمة:
- تأكد من تفعيل وضع المطور
- تأكد من وضع الملفات في المجلد الصحيح
- أعد تشغيل Illustrator

#### الإضافة تظهر لكن لا تعمل:
- تأكد من وجود جميع الملفات المطلوبة
- افتح Developer Tools (F12) للتحقق من الأخطاء
- تأكد من إصدار Illustrator المدعوم

#### رسائل خطأ:
- تأكد من وجود عناصر محددة في Illustrator
- تأكد من أن العناصر تحتوي على ألوان
- جرب إعادة تشغيل الإضافة

### الدعم والتواصل
للدعم الفني أو الاقتراحات، يرجى التواصل مع المطور:
- **المطور:** صلاح الدين الدروبي
- **البريد الإلكتروني:** [البريد الإلكتروني]

### الترخيص
هذه الإضافة مجانية للاستخدام الشخصي والتجاري.

### سجل التحديثات

#### الإصدار V.0.0.01 (الحالي)
- الإصدار الأولي
- ميزة استخراج الألوان
- ميزة معلومات التحديد
- واجهة مستخدم باللغة العربية
- دعم الثيمات الفاتحة والداكنة

---

© 2024 صلاح الدين الدروبي - جميع الحقوق محفوظة
