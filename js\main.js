// Main JavaScript file for All in One Extension
(function() {
    'use strict';

    let csInterface;
    
    // Initialize the extension
    function init() {
        csInterface = new CSInterface();
        setupEventListeners();
        setupTheme();
        console.log('All in One Extension initialized');
    }

    // Setup event listeners
    function setupEventListeners() {
        // Tab switching
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanels = document.querySelectorAll('.tab-panel');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                
                // Remove active class from all tabs and panels
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanels.forEach(panel => panel.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding panel
                button.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Color extraction buttons
        document.getElementById('extract-colors').addEventListener('click', extractColorsFromSelection);
        document.getElementById('get-selection-info').addEventListener('click', getSelectionInfo);

        // Drop zone events
        const dropZone = document.getElementById('drop-zone');
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('drop', handleDrop);
        dropZone.addEventListener('click', extractColorsFromSelection);
    }

    // Setup theme based on Illustrator's theme
    function setupTheme() {
        const hostEnvironment = csInterface.getHostEnvironment();
        const appSkinInfo = JSON.parse(window.__adobe_cep__.getHostEnvironment()).appSkinInfo;
        
        // Adjust theme based on Illustrator's theme
        if (appSkinInfo.panelBackgroundColor.color.red > 128) {
            document.body.classList.add('light-theme');
        } else {
            document.body.classList.add('dark-theme');
        }
    }

    // Extract colors from selected objects
    function extractColorsFromSelection() {
        showLoading('جاري استخراج الألوان...');
        
        csInterface.evalScript('extractColorsFromSelection()', function(result) {
            hideLoading();
            
            try {
                const colors = JSON.parse(result);
                displayColors(colors);
            } catch (error) {
                console.error('Error parsing colors:', error);
                showError('حدث خطأ في استخراج الألوان');
            }
        });
    }

    // Get selection information
    function getSelectionInfo() {
        showLoading('جاري جمع معلومات التحديد...');
        
        csInterface.evalScript('getSelectionInfo()', function(result) {
            hideLoading();
            
            try {
                const info = JSON.parse(result);
                displaySelectionInfo(info);
            } catch (error) {
                console.error('Error parsing selection info:', error);
                showError('حدث خطأ في جمع المعلومات');
            }
        });
    }

    // Display extracted colors
    function displayColors(colors) {
        const colorList = document.getElementById('color-list');
        colorList.innerHTML = '';

        if (!colors || colors.length === 0) {
            colorList.innerHTML = '<p style="text-align: center; color: #b0b0b0; font-size: 11px;">لم يتم العثور على ألوان في التحديد</p>';
            return;
        }

        colors.forEach((color, index) => {
            const colorItem = createColorItem(color, index);
            colorList.appendChild(colorItem);
        });
    }

    // Create color item element
    function createColorItem(color, index) {
        const item = document.createElement('div');
        item.className = 'color-item';
        item.setAttribute('data-color-index', index);

        const swatch = document.createElement('div');
        swatch.className = 'color-swatch';
        swatch.style.backgroundColor = color.hex;

        const info = document.createElement('div');
        info.className = 'color-info';

        const value = document.createElement('div');
        value.className = 'color-value';
        value.textContent = color.hex;

        const type = document.createElement('div');
        type.className = 'color-type';
        type.textContent = `${color.type} | RGB(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b})`;

        info.appendChild(value);
        info.appendChild(type);
        item.appendChild(swatch);
        item.appendChild(info);

        // Add click event to copy color value
        item.addEventListener('click', () => {
            copyToClipboard(color.hex);
            showNotification(`تم نسخ اللون: ${color.hex}`);
        });

        return item;
    }

    // Display selection information
    function displaySelectionInfo(info) {
        const colorList = document.getElementById('color-list');
        colorList.innerHTML = '';

        const infoDiv = document.createElement('div');
        infoDiv.style.cssText = 'background: #2a2a2a; padding: 12px; border-radius: 6px; font-size: 11px;';
        
        infoDiv.innerHTML = `
            <h4 style="color: #4a90e2; margin-bottom: 8px;">معلومات التحديد:</h4>
            <p><strong>عدد العناصر:</strong> ${info.count}</p>
            <p><strong>النوع:</strong> ${info.type}</p>
            <p><strong>الأبعاد:</strong> ${info.width} × ${info.height}</p>
            <p><strong>الموقع:</strong> X: ${info.x}, Y: ${info.y}</p>
        `;

        colorList.appendChild(infoDiv);
    }

    // Drag and drop handlers
    function handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('drag-over');
    }

    function handleDragLeave(e) {
        e.currentTarget.classList.remove('drag-over');
    }

    function handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-over');
        extractColorsFromSelection();
    }

    // Utility functions
    function showLoading(message) {
        const colorList = document.getElementById('color-list');
        colorList.innerHTML = `
            <div style="text-align: center; padding: 20px;">
                <div style="color: #4a90e2; margin-bottom: 10px;">⟳</div>
                <p style="color: #b0b0b0; font-size: 11px;">${message}</p>
            </div>
        `;
    }

    function hideLoading() {
        // Loading will be replaced by results
    }

    function showError(message) {
        const colorList = document.getElementById('color-list');
        colorList.innerHTML = `
            <div style="text-align: center; padding: 20px; color: #ff6b6b;">
                <p style="font-size: 11px;">${message}</p>
            </div>
        `;
    }

    function showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #4a90e2;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 11px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    function copyToClipboard(text) {
        // Use CEP clipboard API if available, otherwise fallback
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    // Initialize when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
