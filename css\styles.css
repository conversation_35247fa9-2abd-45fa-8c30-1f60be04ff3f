/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #2c2c2c;
    color: #e0e0e0;
    font-size: 12px;
    line-height: 1.4;
    direction: rtl;
    text-align: right;
}

.container {
    padding: 10px;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    border-radius: 8px;
    color: white;
}

.header h1 {
    font-size: 16px;
    margin-bottom: 5px;
    font-weight: 600;
}

.version, .developer {
    font-size: 10px;
    opacity: 0.9;
    margin: 2px 0;
}

/* Tabs */
.tabs {
    display: flex;
    margin-bottom: 15px;
    background-color: #3a3a3a;
    border-radius: 6px;
    overflow: hidden;
}

.tab-button {
    flex: 1;
    padding: 8px 12px;
    background: none;
    border: none;
    color: #b0b0b0;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.3s ease;
}

.tab-button:hover {
    background-color: #4a4a4a;
    color: #e0e0e0;
}

.tab-button.active {
    background-color: #4a90e2;
    color: white;
}

/* Tab Content */
.tab-content {
    flex: 1;
    overflow-y: auto;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* Sections */
.section {
    background-color: #3a3a3a;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.section h3 {
    font-size: 14px;
    margin-bottom: 8px;
    color: #4a90e2;
}

.description {
    font-size: 11px;
    color: #b0b0b0;
    margin-bottom: 15px;
}

/* Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.primary-btn, .secondary-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.3s ease;
    flex: 1;
}

.primary-btn {
    background-color: #4a90e2;
    color: white;
}

.primary-btn:hover {
    background-color: #357abd;
}

.secondary-btn {
    background-color: #5a5a5a;
    color: #e0e0e0;
}

.secondary-btn:hover {
    background-color: #6a6a6a;
}

/* Drop Zone */
.drop-zone {
    border: 2px dashed #5a5a5a;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.drop-zone:hover {
    border-color: #4a90e2;
    background-color: rgba(74, 144, 226, 0.1);
}

.drop-zone.drag-over {
    border-color: #4a90e2;
    background-color: rgba(74, 144, 226, 0.2);
}

.drop-content svg {
    color: #5a5a5a;
    margin-bottom: 10px;
}

.drop-content p {
    font-size: 11px;
    color: #b0b0b0;
    margin: 5px 0;
}

.drop-content .small {
    font-size: 10px;
    opacity: 0.7;
}

/* Results */
.results {
    background-color: #2a2a2a;
    border-radius: 6px;
    padding: 12px;
    margin-top: 10px;
}

.results h4 {
    font-size: 12px;
    margin-bottom: 10px;
    color: #4a90e2;
}

.color-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.color-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    background-color: #3a3a3a;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.color-item:hover {
    background-color: #4a4a4a;
}

.color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #5a5a5a;
    flex-shrink: 0;
}

.color-info {
    flex: 1;
    font-size: 10px;
}

.color-value {
    font-weight: 600;
    margin-bottom: 2px;
}

.color-type {
    color: #b0b0b0;
    font-size: 9px;
}

/* Tool Grid */
.tool-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.tool-card {
    background-color: #2a2a2a;
    border-radius: 6px;
    padding: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.tool-card:not(.disabled) {
    cursor: pointer;
}

.tool-card:not(.disabled):hover {
    background-color: #3a3a3a;
    transform: translateY(-2px);
}

.tool-card.disabled {
    opacity: 0.6;
}

.tool-card h4 {
    font-size: 12px;
    margin-bottom: 5px;
    color: #4a90e2;
}

.tool-card p {
    font-size: 10px;
    color: #b0b0b0;
}

.coming-soon {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #ff6b6b;
    color: white;
    font-size: 8px;
    padding: 2px 6px;
    border-radius: 10px;
}

/* Footer */
.footer {
    text-align: center;
    padding: 10px;
    font-size: 9px;
    color: #7a7a7a;
    border-top: 1px solid #4a4a4a;
    margin-top: auto;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
    background: #5a5a5a;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6a6a6a;
}
