// Host script for Adobe Illustrator - All in One Extension
// This script runs in the Illustrator environment

// Extract colors from selected objects
function extractColorsFromSelection() {
    try {
        var doc = app.activeDocument;
        var selection = doc.selection;
        var colors = [];
        
        if (selection.length === 0) {
            return JSON.stringify([]);
        }
        
        for (var i = 0; i < selection.length; i++) {
            var item = selection[i];
            extractColorsFromItem(item, colors);
        }
        
        // Remove duplicates
        var uniqueColors = removeDuplicateColors(colors);
        
        return JSON.stringify(uniqueColors);
        
    } catch (error) {
        return JSON.stringify({error: error.toString()});
    }
}

// Extract colors from a single item
function extractColorsFromItem(item, colors) {
    try {
        // Handle different types of objects
        switch (item.typename) {
            case "PathItem":
                extractPathItemColors(item, colors);
                break;
            case "TextFrame":
                extractTextColors(item, colors);
                break;
            case "GroupItem":
                extractGroupColors(item, colors);
                break;
            case "CompoundPathItem":
                extractCompoundPathColors(item, colors);
                break;
            case "RasterItem":
                // Raster items don't have extractable colors in the same way
                break;
        }
    } catch (error) {
        // Continue processing other items
    }
}

// Extract colors from path items
function extractPathItemColors(pathItem, colors) {
    try {
        // Fill color
        if (pathItem.filled && pathItem.fillColor) {
            var fillColor = convertColorToHex(pathItem.fillColor);
            if (fillColor) {
                colors.push({
                    hex: fillColor.hex,
                    rgb: fillColor.rgb,
                    type: "Fill",
                    source: "PathItem"
                });
            }
        }
        
        // Stroke color
        if (pathItem.stroked && pathItem.strokeColor) {
            var strokeColor = convertColorToHex(pathItem.strokeColor);
            if (strokeColor) {
                colors.push({
                    hex: strokeColor.hex,
                    rgb: strokeColor.rgb,
                    type: "Stroke",
                    source: "PathItem"
                });
            }
        }
    } catch (error) {
        // Continue processing
    }
}

// Extract colors from text
function extractTextColors(textFrame, colors) {
    try {
        var textRange = textFrame.textRange;
        
        // Get character attributes
        for (var i = 0; i < textRange.characters.length; i++) {
            try {
                var char = textRange.characters[i];
                var fillColor = convertColorToHex(char.characterAttributes.fillColor);
                
                if (fillColor) {
                    colors.push({
                        hex: fillColor.hex,
                        rgb: fillColor.rgb,
                        type: "Text Fill",
                        source: "TextFrame"
                    });
                }
                
                if (char.characterAttributes.stroked) {
                    var strokeColor = convertColorToHex(char.characterAttributes.strokeColor);
                    if (strokeColor) {
                        colors.push({
                            hex: strokeColor.hex,
                            rgb: strokeColor.rgb,
                            type: "Text Stroke",
                            source: "TextFrame"
                        });
                    }
                }
            } catch (charError) {
                // Continue with next character
            }
        }
    } catch (error) {
        // Continue processing
    }
}

// Extract colors from groups
function extractGroupColors(groupItem, colors) {
    try {
        for (var i = 0; i < groupItem.pageItems.length; i++) {
            extractColorsFromItem(groupItem.pageItems[i], colors);
        }
    } catch (error) {
        // Continue processing
    }
}

// Extract colors from compound paths
function extractCompoundPathColors(compoundPath, colors) {
    try {
        for (var i = 0; i < compoundPath.pathItems.length; i++) {
            extractPathItemColors(compoundPath.pathItems[i], colors);
        }
    } catch (error) {
        // Continue processing
    }
}

// Convert Illustrator color to hex
function convertColorToHex(color) {
    try {
        var r, g, b;
        
        switch (color.typename) {
            case "RGBColor":
                r = Math.round(color.red);
                g = Math.round(color.green);
                b = Math.round(color.blue);
                break;
                
            case "CMYKColor":
                // Convert CMYK to RGB
                var rgb = cmykToRgb(color.cyan, color.magenta, color.yellow, color.black);
                r = rgb.r;
                g = rgb.g;
                b = rgb.b;
                break;
                
            case "GrayColor":
                // Convert grayscale to RGB
                var gray = Math.round((100 - color.gray) * 2.55);
                r = g = b = gray;
                break;
                
            case "SpotColor":
                // Try to get the spot color's RGB equivalent
                var spotRgb = spotToRgb(color);
                r = spotRgb.r;
                g = spotRgb.g;
                b = spotRgb.b;
                break;
                
            default:
                return null;
        }
        
        // Ensure values are within valid range
        r = Math.max(0, Math.min(255, r));
        g = Math.max(0, Math.min(255, g));
        b = Math.max(0, Math.min(255, b));
        
        var hex = "#" + 
                  ("0" + r.toString(16)).slice(-2) + 
                  ("0" + g.toString(16)).slice(-2) + 
                  ("0" + b.toString(16)).slice(-2);
        
        return {
            hex: hex.toUpperCase(),
            rgb: {r: r, g: g, b: b}
        };
        
    } catch (error) {
        return null;
    }
}

// Convert CMYK to RGB
function cmykToRgb(c, m, y, k) {
    c = c / 100;
    m = m / 100;
    y = y / 100;
    k = k / 100;
    
    var r = Math.round(255 * (1 - c) * (1 - k));
    var g = Math.round(255 * (1 - m) * (1 - k));
    var b = Math.round(255 * (1 - y) * (1 - k));
    
    return {r: r, g: g, b: b};
}

// Convert spot color to RGB (simplified)
function spotToRgb(spotColor) {
    try {
        // This is a simplified conversion
        // In reality, spot colors would need proper color management
        var tint = spotColor.tint / 100;
        
        // Default to a neutral color if we can't determine the actual color
        var r = Math.round(128 * (1 - tint) + 255 * tint);
        var g = Math.round(128 * (1 - tint) + 255 * tint);
        var b = Math.round(128 * (1 - tint) + 255 * tint);
        
        return {r: r, g: g, b: b};
    } catch (error) {
        return {r: 128, g: 128, b: 128};
    }
}

// Remove duplicate colors
function removeDuplicateColors(colors) {
    var unique = [];
    var seen = {};
    
    for (var i = 0; i < colors.length; i++) {
        var color = colors[i];
        var key = color.hex + "_" + color.type;
        
        if (!seen[key]) {
            seen[key] = true;
            unique.push(color);
        }
    }
    
    return unique;
}

// Get selection information
function getSelectionInfo() {
    try {
        var doc = app.activeDocument;
        var selection = doc.selection;
        
        if (selection.length === 0) {
            return JSON.stringify({
                count: 0,
                type: "None",
                width: 0,
                height: 0,
                x: 0,
                y: 0
            });
        }
        
        var bounds = getSelectionBounds(selection);
        var types = getSelectionTypes(selection);
        
        return JSON.stringify({
            count: selection.length,
            type: types,
            width: Math.round(bounds.width * 100) / 100,
            height: Math.round(bounds.height * 100) / 100,
            x: Math.round(bounds.left * 100) / 100,
            y: Math.round(bounds.top * 100) / 100
        });
        
    } catch (error) {
        return JSON.stringify({error: error.toString()});
    }
}

// Get bounds of selection
function getSelectionBounds(selection) {
    var left = Infinity;
    var top = -Infinity;
    var right = -Infinity;
    var bottom = Infinity;
    
    for (var i = 0; i < selection.length; i++) {
        var item = selection[i];
        var bounds = item.geometricBounds;
        
        left = Math.min(left, bounds[0]);
        top = Math.max(top, bounds[1]);
        right = Math.max(right, bounds[2]);
        bottom = Math.min(bottom, bounds[3]);
    }
    
    return {
        left: left,
        top: top,
        width: right - left,
        height: top - bottom
    };
}

// Get types of selected objects
function getSelectionTypes(selection) {
    var types = {};
    
    for (var i = 0; i < selection.length; i++) {
        var type = selection[i].typename;
        types[type] = (types[type] || 0) + 1;
    }
    
    var typeStrings = [];
    for (var type in types) {
        typeStrings.push(type + " (" + types[type] + ")");
    }
    
    return typeStrings.join(", ");
}
